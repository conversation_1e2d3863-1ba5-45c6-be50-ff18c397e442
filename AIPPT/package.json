{"name": "ppt-download-demo", "version": "1.0.0", "description": "PPT下载功能演示，使用本地画布数据，跳过VIP验证", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["ppt", "download", "canvas", "demo", "nodejs"], "author": "AI Assistant", "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "local"}, "dependencies": {}, "devDependencies": {}, "files": ["server.js", "download-demo.html", "20250828161341ckdwaqo.json", "README.md"]}