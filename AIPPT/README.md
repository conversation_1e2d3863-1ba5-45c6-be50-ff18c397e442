# PPT下载演示

这是一个基于Node.js的PPT下载功能演示项目，使用本地画布数据，跳过VIP验证，直接进行各种格式的下载。

## 🚀 快速开始

### 1. 启动服务器

```bash
node server.js
```

或者使用npm：

```bash
npm start
```

### 2. 打开浏览器

访问 `http://localhost:3000` 查看演示页面。

## 📁 项目结构

```
├── server.js                    # Node.js HTTP服务器
├── download-demo.html           # 演示网页
├── 20250828161341ckdwaqo.json  # 画布数据文件
├── package.json                 # 项目配置
└── README.md                   # 说明文档
```

## 🎯 功能特性

### ✅ 已实现功能

1. **PPT下载**
   - 标准PPT格式
   - 可编辑PPT格式
   - 本地生成，无需服务端

2. **图片下载**
   - PNG格式导出
   - JPEG格式导出
   - 支持单张或批量打包

3. **长图导出**
   - 将多页PPT合并为单张长图
   - PNG格式输出

### ❌ 需要服务端支持的功能

1. **PDF下载**
   - 需要服务端转换接口
   - 当前为演示模式

## 🔧 技术实现

### 核心特性

- **跳过VIP验证**: 模拟VIP状态，始终返回有权限
- **本地数据处理**: 直接使用JSON画布数据
- **模拟布局引擎**: 实现基本的导出功能
- **前端生成**: PPT和图片完全在浏览器中生成

### 关键组件

1. **画布数据加载**: 从JSON文件加载设计数据
2. **布局引擎模拟**: 模拟原始的布局处理功能
3. **文件生成**: 使用Canvas API和Blob生成文件
4. **下载处理**: 创建临时URL进行文件下载

## 📊 画布数据格式

画布数据包含以下主要结构：

```json
{
  "version": "0.0.23",
  "pageType": "page",
  "global": {
    "width": 960,
    "height": 540,
    "background": {
      "type": "color",
      "data": "#FFFFFF"
    }
  },
  "layouts": [
    {
      "elements": [
        // 页面元素数据
      ]
    }
  ]
}
```

## 🛠️ 自定义配置

### 修改服务器端口

```bash
PORT=3001 node server.js
```

### 修改画布数据

替换 `20250828161341ckdwaqo.json` 文件为您自己的画布数据。

### 扩展下载功能

在 `download-demo.html` 中的相应函数中添加您的自定义逻辑：

- `downloadPPT()`: PPT下载逻辑
- `downloadPicture()`: 图片下载逻辑
- `downloadToImage()`: 长图导出逻辑

## 🔍 调试信息

### 浏览器控制台

打开浏览器开发者工具，在控制台中可以看到：
- 画布数据加载状态
- 文件生成过程
- 错误信息和调试日志

### 服务器日志

服务器启动时会显示：
- 服务地址和端口
- 当前目录文件列表
- 使用说明

## ⚠️ 注意事项

1. **字体支持**: 当前使用系统默认字体，可能与原始设计有差异
2. **复杂元素**: 复杂的图形元素可能显示简化
3. **文件大小**: 生成的文件可能与原始文件大小不同
4. **兼容性**: 需要现代浏览器支持Canvas和Blob API

## 🚧 已知限制

1. PDF导出需要服务端支持
2. 字体嵌入功能为模拟实现
3. 复杂动画和效果无法完全还原
4. 图片资源需要网络访问

## 📝 更新日志

### v1.0.0
- 初始版本
- 支持PPT、图片、长图下载
- 跳过VIP验证
- 使用本地画布数据

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个演示项目。

## 📄 许可证

MIT License
