<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT下载演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .download-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .download-section h3 {
            margin-top: 0;
            color: #2BAC33;
        }
        button {
            background-color: #2BAC33;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #208126;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .canvas-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .canvas-info h4 {
            margin-top: 0;
            color: #495057;
        }
        .canvas-info p {
            margin: 5px 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 PPT下载演示</h1>
        
        <div class="canvas-info">
            <h4>画布信息</h4>
            <p><strong>文件:</strong> 20250828161341ckdwaqo.json</p>
            <p><strong>尺寸:</strong> <span id="canvas-size">加载中...</span></p>
            <p><strong>页面数:</strong> <span id="canvas-pages">加载中...</span></p>
            <p><strong>版本:</strong> <span id="canvas-version">加载中...</span></p>
        </div>

        <div class="status" id="status"></div>

        <div class="download-section">
            <h3>📄 PPT下载</h3>
            <p>导出为PowerPoint格式，支持编辑和字体嵌入</p>
            <button onclick="downloadPPT('normal')">下载PPT (标准)</button>
            <button onclick="downloadPPT('enedit')">下载PPT (可编辑)</button>
        </div>

        <div class="download-section">
            <h3>🖼️ 图片下载</h3>
            <p>导出为图片格式，支持PNG和JPEG</p>
            <button onclick="downloadPicture('png')">下载PNG图片</button>
            <button onclick="downloadPicture('jpeg')">下载JPEG图片</button>
        </div>

        <div class="download-section">
            <h3>📑 长图导出</h3>
            <p>将PPT转换为单张长图</p>
            <button onclick="downloadToImage()">导出长图</button>
        </div>

        <div class="download-section">
            <h3>📋 PDF下载</h3>
            <p>导出为PDF格式（需要服务端支持）</p>
            <button onclick="downloadPDF()" disabled>下载PDF (需要接口)</button>
        </div>
    </div>

    <!-- 引入必要的依赖 -->
    <script type="module">
        // 模拟必要的依赖和环境
        let canvasData = null;
        let downloadModule = null;

        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }

        // 加载画布数据
        async function loadCanvasData() {
            try {
                const response = await fetch('./20250828161341ckdwaqo.json');
                canvasData = await response.json();
                
                // 更新画布信息显示
                document.getElementById('canvas-size').textContent = 
                    `${canvasData.global.width} × ${canvasData.global.height}`;
                document.getElementById('canvas-pages').textContent = 
                    canvasData.layouts ? canvasData.layouts.length : 1;
                document.getElementById('canvas-version').textContent = 
                    canvasData.version || '未知';
                
                showStatus('画布数据加载成功！', 'success');
                return canvasData;
            } catch (error) {
                showStatus('画布数据加载失败: ' + error.message, 'error');
                console.error('加载画布数据失败:', error);
                return null;
            }
        }

        // 模拟VIP状态检查（始终返回true）
        function mockVipCheck() {
            return {
                state: {
                    user: {
                        is_download_vip: true,
                        is_vip: true,
                        vip_type: 1
                    },
                    isNatureUser: false,
                    aiCredits: {
                        thirdPart: null
                    }
                },
                getAiCredits: () => Promise.resolve()
            };
        }

        // 模拟字体列表
        function mockFontsList() {
            return [
                {
                    url: "Source Han Sans CN Normal",
                    eot_file_url: "data:application/font-woff2;base64,", // 模拟字体数据
                    name: "Source Han Sans CN Normal"
                }
            ];
        }

        // 模拟布局引擎
        function mockLayoutEngine() {
            return {
                importPages: (data) => {
                    console.log('导入页面数据:', data);
                },
                exportPPTX: async (isEdit, options) => {
                    console.log('导出PPTX:', isEdit, options);
                    // 创建一个模拟的PPTX blob
                    const mockPptxContent = new Uint8Array([
                        0x50, 0x4B, 0x03, 0x04, // ZIP文件头
                        // ... 更多PPTX数据
                    ]);
                    return new Blob([mockPptxContent], {
                        type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                    });
                },
                exportPagesImage: async (format, quality, scale, transparent, pages) => {
                    console.log('导出图片:', format, quality, scale);
                    // 创建模拟的图片数据
                    const canvas = document.createElement('canvas');
                    canvas.width = canvasData.global.width;
                    canvas.height = canvasData.global.height;
                    const ctx = canvas.getContext('2d');
                    
                    // 绘制简单的背景
                    ctx.fillStyle = canvasData.global.background.data || '#FFFFFF';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 添加文本
                    ctx.fillStyle = '#333';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('PPT演示页面', canvas.width/2, canvas.height/2);
                    
                    return new Promise(resolve => {
                        canvas.toBlob(blob => {
                            resolve([blob]);
                        }, format === 'png' ? 'image/png' : 'image/jpeg', quality);
                    });
                }
            };
        }

        // 全局函数定义
        window.downloadPPT = async function(type) {
            if (!canvasData) {
                showStatus('请先加载画布数据', 'error');
                return;
            }

            try {
                showStatus('正在生成PPT...', 'info');
                
                const layoutEngine = mockLayoutEngine();
                const fontsList = mockFontsList();
                
                // 导入页面数据
                layoutEngine.importPages(canvasData);
                
                // 导出PPTX
                const pptxBlob = await layoutEngine.exportPPTX(type === 'enedit', {
                    embedFonts: true,
                    getSubFont: async (fontUrl) => {
                        // 模拟字体处理
                        return new ArrayBuffer(1024);
                    }
                });

                // 下载文件
                const url = URL.createObjectURL(pptxBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `演示文档_${type}.pptx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showStatus('PPT下载成功！', 'success');
            } catch (error) {
                showStatus('PPT下载失败: ' + error.message, 'error');
                console.error('PPT下载错误:', error);
            }
        };

        window.downloadPicture = async function(format) {
            if (!canvasData) {
                showStatus('请先加载画布数据', 'error');
                return;
            }

            try {
                showStatus(`正在生成${format.toUpperCase()}图片...`, 'info');
                
                const layoutEngine = mockLayoutEngine();
                layoutEngine.importPages(canvasData);
                
                // 导出图片
                const images = await layoutEngine.exportPagesImage(
                    `images/${format}`, 0.6, 1, true, []
                );

                if (images.length === 1) {
                    // 单张图片直接下载
                    const url = URL.createObjectURL(images[0]);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `演示文档.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } else {
                    // 多张图片打包为ZIP
                    const JSZip = (await import('https://cdn.skypack.dev/jszip')).default;
                    const zip = new JSZip();
                    
                    images.forEach((blob, index) => {
                        zip.file(`演示文档-${index + 1}.${format}`, blob);
                    });

                    const zipBlob = await zip.generateAsync({
                        type: 'blob',
                        compression: 'DEFLATE',
                        compressionOptions: { level: 9 }
                    });

                    const url = URL.createObjectURL(zipBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `演示文档_图片.zip`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }

                showStatus(`${format.toUpperCase()}图片下载成功！`, 'success');
            } catch (error) {
                showStatus(`图片下载失败: ${error.message}`, 'error');
                console.error('图片下载错误:', error);
            }
        };

        window.downloadToImage = async function() {
            if (!canvasData) {
                showStatus('请先加载画布数据', 'error');
                return;
            }

            try {
                showStatus('正在生成长图...', 'info');
                
                // 创建长图画布
                const canvas = document.createElement('canvas');
                const totalHeight = canvasData.layouts ? 
                    canvasData.layouts.length * canvasData.global.height : 
                    canvasData.global.height;
                
                canvas.width = canvasData.global.width;
                canvas.height = totalHeight;
                const ctx = canvas.getContext('2d');
                
                // 绘制背景
                ctx.fillStyle = canvasData.global.background.data || '#FFFFFF';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 添加页面内容（简化版）
                ctx.fillStyle = '#333';
                ctx.font = '32px Arial';
                ctx.textAlign = 'center';
                
                if (canvasData.layouts) {
                    canvasData.layouts.forEach((layout, index) => {
                        const y = index * canvasData.global.height + canvasData.global.height / 2;
                        ctx.fillText(`第 ${index + 1} 页`, canvas.width / 2, y);
                    });
                } else {
                    ctx.fillText('PPT演示页面', canvas.width / 2, canvas.height / 2);
                }

                // 转换为blob并下载
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '演示文档_长图.png';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    showStatus('长图下载成功！', 'success');
                }, 'image/png');

            } catch (error) {
                showStatus('长图生成失败: ' + error.message, 'error');
                console.error('长图生成错误:', error);
            }
        };

        window.downloadPDF = function() {
            showStatus('PDF下载需要服务端接口支持，当前为演示模式', 'error');
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadCanvasData();
        });
    </script>
</body>
</html>
