<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT下载演示 - 使用原始下载模块</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .download-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .download-section h3 {
            margin-top: 0;
            color: #2BAC33;
        }
        button {
            background-color: #2BAC33;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #208126;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .canvas-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .canvas-info h4 {
            margin-top: 0;
            color: #495057;
        }
        .canvas-info p {
            margin: 5px 0;
            color: #6c757d;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .warning h4 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 PPT下载演示 - 原始模块</h1>
        
        <div class="warning">
            <h4>⚠️ 注意</h4>
            <p>此演示直接使用原始的 <code>use-download-0220c3c4.js</code> 下载模块。</p>
            <p>已跳过VIP验证，直接使用本地画布数据进行下载。</p>
        </div>
        
        <div class="canvas-info">
            <h4>画布信息</h4>
            <p><strong>文件:</strong> 20250828161341ckdwaqo.json</p>
            <p><strong>尺寸:</strong> <span id="canvas-size">加载中...</span></p>
            <p><strong>页面数:</strong> <span id="canvas-pages">加载中...</span></p>
            <p><strong>版本:</strong> <span id="canvas-version">加载中...</span></p>
        </div>

        <div class="status" id="status"></div>

        <div class="download-section">
            <h3>📄 PPT下载</h3>
            <p>使用原始下载模块导出PowerPoint格式</p>
            <button onclick="downloadWithOriginalModule('ppt', 'normal')">下载PPT (标准)</button>
            <button onclick="downloadWithOriginalModule('ppt', 'enedit')">下载PPT (可编辑)</button>
        </div>

        <div class="download-section">
            <h3>🖼️ 图片下载</h3>
            <p>使用原始下载模块导出图片格式</p>
            <button onclick="downloadWithOriginalModule('picture', 'png')">下载PNG图片</button>
            <button onclick="downloadWithOriginalModule('picture', 'jpeg')">下载JPEG图片</button>
        </div>

        <div class="download-section">
            <h3>📑 长图导出</h3>
            <p>使用原始下载模块转换为长图</p>
            <button onclick="downloadWithOriginalModule('toImage')">导出长图</button>
        </div>

        <div class="download-section">
            <h3>📋 PDF下载</h3>
            <p>使用原始下载模块导出PDF（需要接口支持）</p>
            <button onclick="downloadWithOriginalModule('pdf')" disabled>下载PDF (需要接口)</button>
        </div>
    </div>

    <!-- 引入原始的下载模块和依赖 -->
    <script type="module">
        // 导入原始的下载模块
        import { u as useDownload } from './use-download-0220c3c4.js';
        
        let canvasData = null;
        let downloadInstance = null;

        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }

        // 模拟必要的全局对象和函数
        window.mockGlobals = function() {
            // 模拟状态管理
            const mockState = {
                state: {
                    user: {
                        is_download_vip: true,
                        is_vip: true,
                        vip_type: 1
                    },
                    isNatureUser: false,
                    aiCredits: {
                        thirdPart: null
                    }
                },
                getAiCredits: () => Promise.resolve()
            };

            // 模拟路由查询
            const mockQuery = {
                third_v1_vip: 1
            };

            // 模拟翻译函数
            const mockT = (key) => {
                const translations = {
                    'editor.header.exporPicName': '_图片'
                };
                return translations[key] || key;
            };

            // 模拟消息提示
            const mockMessage = {
                warning: (msg) => {
                    showStatus(msg, 'error');
                    console.warn('Warning:', msg);
                },
                error: (msg) => {
                    showStatus(msg, 'error');
                    console.error('Error:', msg);
                }
            };

            return {
                state: mockState,
                query: mockQuery,
                t: mockT,
                message: mockMessage
            };
        };

        // 加载画布数据
        async function loadCanvasData() {
            try {
                const response = await fetch('./20250828161341ckdwaqo.json');
                canvasData = await response.json();
                
                // 更新画布信息显示
                document.getElementById('canvas-size').textContent = 
                    `${canvasData.global.width} × ${canvasData.global.height}`;
                document.getElementById('canvas-pages').textContent = 
                    canvasData.layouts ? canvasData.layouts.length : 1;
                document.getElementById('canvas-version').textContent = 
                    canvasData.version || '未知';
                
                showStatus('画布数据加载成功！', 'success');
                return canvasData;
            } catch (error) {
                showStatus('画布数据加载失败: ' + error.message, 'error');
                console.error('加载画布数据失败:', error);
                return null;
            }
        }

        // 初始化下载实例
        async function initDownloadInstance() {
            try {
                // 初始化全局模拟对象
                const globals = mockGlobals();
                
                // 创建下载实例
                downloadInstance = useDownload();
                
                showStatus('下载模块初始化成功！', 'success');
                return downloadInstance;
            } catch (error) {
                showStatus('下载模块初始化失败: ' + error.message, 'error');
                console.error('下载模块初始化失败:', error);
                return null;
            }
        }

        // 使用原始模块进行下载
        window.downloadWithOriginalModule = async function(type, prop) {
            if (!canvasData) {
                showStatus('请先加载画布数据', 'error');
                return;
            }

            if (!downloadInstance) {
                showStatus('下载模块未初始化', 'error');
                return;
            }

            try {
                showStatus(`正在使用原始模块下载${type}...`, 'info');
                
                // 构建下载参数
                const downloadParams = {
                    type: type,
                    prop: prop,
                    designId: 'demo-design-id',
                    designName: '演示文档',
                    canvasData: canvasData,
                    handleShowPayModal: () => {
                        showStatus('VIP验证已跳过', 'info');
                    },
                    onStartDownload: () => {
                        showStatus('开始下载...', 'info');
                    },
                    onSuccessDownload: () => {
                        showStatus('下载成功！', 'success');
                    },
                    onErrorDownload: (error) => {
                        showStatus('下载失败: ' + error.message, 'error');
                    }
                };

                // 调用原始下载函数
                await downloadInstance.download(downloadParams);
                
            } catch (error) {
                showStatus('下载过程中发生错误: ' + error.message, 'error');
                console.error('下载错误:', error);
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await loadCanvasData();
            await initDownloadInstance();
        });
    </script>
</body>
</html>
